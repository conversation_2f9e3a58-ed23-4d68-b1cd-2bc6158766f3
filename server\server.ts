import { tracker<PERSON>s<PERSON><PERSON>r, gps<PERSON><PERSON><PERSON>, startTrackerServer, shutdownTrackerServer } from './tracker-server.js';
import { webClientWsServer, authService, initializeWebClientServer, startWebClientServer, shutdownWebClientServer } from './web-client-server.js';

// Main server coordinator
class ServerCoordinator {
  private isShuttingDown = false;

  async start() {
    try {
      console.log('🚀 Starting GPS Tracker Management System...');

      // Initialize the web client server with the shared GPS handler
      const webClientServer = initializeWebClientServer(gpsHandler);

      // Set up communication between tracker server and web client server
      trackerWsServer.setWebClientServer(webClientServer);

      // Start both servers
      await Promise.all([
        startTrackerServer(),
        startWebClientServer()
      ]);

      console.log('✅ All servers started successfully!');
      console.log('📡 GPS Trackers should connect to: ws://localhost:8090');
      console.log('🌐 Web clients should connect to: ws://localhost:3000/ws');
      console.log('🌍 Web interface available at: http://localhost:3000');

    } catch (error) {
      console.error('❌ Failed to start servers:', error);
      process.exit(1);
    }
  }

  async shutdown() {
    if (this.isShuttingDown) return;
    this.isShuttingDown = true;

    console.log('🛑 Shutting down GPS Tracker Management System...');

    try {
      await Promise.all([
        shutdownTrackerServer(),
        shutdownWebClientServer()
      ]);

      console.log('✅ All servers shut down gracefully');
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
    }

    process.exit(0);
  }
}

const coordinator = new ServerCoordinator();

// Start the system
coordinator.start();

// Graceful shutdown handlers
process.on('SIGTERM', () => {
  console.log('SIGTERM received');
  coordinator.shutdown();
});

process.on('SIGINT', () => {
  console.log('SIGINT received');
  coordinator.shutdown();
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  coordinator.shutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  coordinator.shutdown();
});

export { authService, gpsHandler, trackerWsServer, webClientWsServer };
