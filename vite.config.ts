import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 5173, // Frontend runs on port 5173
    proxy: {
      '/api': {
        target: 'http://localhost:3000', // Proxy API calls to web client server
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:3000', // Proxy WebSocket connections to web client server
        ws: true,
      },
    },
  },
})
