import { WebSocket, WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { AuthService } from './auth.js';
import { GPSHandler } from './gps-handler.js';
import { WebSocketMessage } from './types.js';

// Tracker WebSocket Server - Port 8090 (no /ws path)
export class TrackerWebSocketServer {
  private wss: WebSocketServer;
  private gpsHandler: GPSHandler;
  private trackerConnections = new Map<string, WebSocket>(); // IMEI -> WebSocket mapping for GPS trackers
  private webClientServer: WebClientWebSocketServer | null = null;

  constructor(server: any, gpsHandler: GPSHandler) {
    this.gpsHandler = gpsHandler;

    this.wss = new WebSocketServer({
      server,
      // No path specified - accepts connections directly to ws://localhost:8090
    });

    // Set up tracker status change callback
    this.gpsHandler.setTrackerStatusChangeCallback((imei: string, isConnected: boolean) => {
      // Notify web client server about tracker status changes
      if (this.webClientServer) {
        this.webClientServer.broadcastTrackerStatusChange(imei, isConnected);
      }
    });

    // Set up periodic check for disconnected trackers
    setInterval(() => {
      this.gpsHandler.checkForDisconnectedTrackers();
    }, 60000); // Check every minute

    this.wss.on('connection', this.handleTrackerConnection.bind(this));
  }

  setWebClientServer(webClientServer: WebClientWebSocketServer): void {
    this.webClientServer = webClientServer;
  }

  private handleTrackerConnection(ws: WebSocket, request: IncomingMessage): void {
    console.log('📡 New GPS Tracker connection');

    ws.on('message', (data: Buffer) => {
      try {
        const message: WebSocketMessage = JSON.parse(data.toString());
        this.handleTrackerMessage(ws, message);
      } catch (error) {
        console.error('Error parsing GPS tracker message:', error);
      }
    });

    ws.on('close', () => {
      console.log('📡 GPS Tracker connection closed');
      this.handleTrackerDisconnection(ws);
    });

    ws.on('error', (error) => {
      console.error('📡 GPS Tracker WebSocket error:', error);
      this.handleTrackerDisconnection(ws);
    });
  }

  private handleTrackerDisconnection(ws: WebSocket): void {
    // Check if this was a tracker connection
    let disconnectedImei: string | null = null;
    for (const [imei, trackerWs] of this.trackerConnections.entries()) {
      if (trackerWs === ws) {
        disconnectedImei = imei;
        this.trackerConnections.delete(imei);
        break;
      }
    }

    // If this was a tracker connection, immediately notify about disconnection
    if (disconnectedImei) {
      console.log(`📡 GPS Tracker ${disconnectedImei} disconnected from WebSocket`);
      this.gpsHandler.handleTrackerDisconnection(disconnectedImei);
    }
  }

  private handleTrackerMessage(ws: WebSocket, message: WebSocketMessage): void {
    console.log(`📨 Received tracker message type: ${message.type}`);

    if (message.type === 'gps_data') {
      console.log(`🛰️ Processing GPS data for IMEI: ${message.data?.imei}`);
      this.handleGPSData(message.data, ws);
    } else {
      console.log(`❌ Unknown message type from tracker: ${message.type}`);
    }
  }

  private handleGPSData(gpsData: any, ws: WebSocket): void {
    console.log(`🔄 Processing GPS data:`, gpsData);
    const message = this.gpsHandler.processGPSData(gpsData);
    if (message) {
      console.log(`✅ GPS message processed for IMEI ${message.imei}, broadcasting to web clients`);

      // Register this WebSocket as a tracker connection if not already registered
      if (!this.trackerConnections.has(message.imei)) {
        this.trackerConnections.set(message.imei, ws);
        console.log(`📡 Registered GPS tracker connection for IMEI ${message.imei}`);
      }

      // Broadcast to web clients through the web client server
      if (this.webClientServer) {
        this.webClientServer.broadcastGPSData(message.imei, {
          type: 'gps_data',
          data: message
        });
      }
    } else {
      console.log(`❌ Failed to process GPS data`);
    }
  }
}

// Web Client WebSocket Server - Port 3000 (with /ws path)
export class WebClientWebSocketServer {
  private wss: WebSocketServer;
  private authService: AuthService;
  private gpsHandler: GPSHandler;
  private clients = new Map<WebSocket, { username?: string; imeiCodes?: string[] }>();

  constructor(server: any, authService: AuthService, gpsHandler: GPSHandler) {
    this.authService = authService;
    this.gpsHandler = gpsHandler;

    this.wss = new WebSocketServer({
      server,
      path: '/ws'
    });

    this.wss.on('connection', this.handleClientConnection.bind(this));
  }

  private handleClientConnection(ws: WebSocket, request: IncomingMessage): void {
    console.log('🌐 New Web Client connection');

    // Initialize client data
    this.clients.set(ws, {});

    ws.on('message', (data: Buffer) => {
      try {
        const message: WebSocketMessage = JSON.parse(data.toString());
        this.handleClientMessage(ws, message);
      } catch (error) {
        console.error('Error parsing Web Client message:', error);
        this.sendError(ws, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      console.log('🌐 Web Client connection closed');
      this.clients.delete(ws);
    });

    ws.on('error', (error) => {
      console.error('🌐 Web Client WebSocket error:', error);
      this.clients.delete(ws);
    });
  }

  private async handleClientMessage(ws: WebSocket, message: WebSocketMessage): Promise<void> {
    const clientData = this.clients.get(ws);
    if (!clientData) return;

    console.log(`📨 Received web client message type: ${message.type}`);

    switch (message.type) {
      case 'auth':
        await this.handleAuth(ws, message.data);
        break;

      case 'voyage_list':
        this.handleVoyageListRequest(ws);
        break;

      case 'message_list':
        this.handleMessageListRequest(ws, message.data);
        break;

      case 'tracker_status':
        this.handleTrackerStatusRequest(ws);
        break;

      default:
        this.sendError(ws, 'Unknown message type');
    }
  }

  private async handleAuth(ws: WebSocket, authData: any): Promise<void> {
    try {
      const { username, password, token } = authData;
      
      let authResult = null;
      
      if (token) {
        // Verify existing token
        authResult = this.authService.verifyToken(token);
      } else if (username && password) {
        // Login with credentials
        const newToken = await this.authService.login(username, password);
        if (newToken) {
          authResult = this.authService.verifyToken(newToken);
          // Send the new token back to client
          this.send(ws, {
            type: 'auth',
            data: { token: newToken, success: true }
          });
        }
      }
      
      if (authResult) {
        const clientData = this.clients.get(ws);
        if (clientData) {
          clientData.username = authResult.username;
          clientData.imeiCodes = authResult.imeiCodes;
        }

        // Always send success message to trigger data loading
        // For new login, include the token; for token verification, just success
        if (token) {
          // Token verification - send success to trigger data loading
          this.send(ws, {
            type: 'auth',
            data: { success: true, imeiCodes: authResult.imeiCodes }
          });
        } else {
          // New login - send success with new token
          this.send(ws, {
            type: 'auth',
            data: { success: true, imeiCodes: authResult.imeiCodes }
          });
        }
      } else {
        this.sendError(ws, 'Authentication failed');
      }
    } catch (error) {
      console.error('Auth error:', error);
      this.sendError(ws, 'Authentication error');
    }
  }

  // Public method to broadcast GPS data to authorized clients (called from TrackerWebSocketServer)
  public broadcastGPSData(imei: string, message: WebSocketMessage): void {
    this.broadcastToAuthorizedClients(imei, message);
  }

  // Public method to broadcast tracker status changes (called from TrackerWebSocketServer)
  public broadcastTrackerStatusChange(imei: string, isConnected: boolean): void {
    console.log(`📡 Broadcasting tracker status change for IMEI ${imei}: ${isConnected ? 'connected' : 'disconnected'}`);

    for (const [ws, clientData] of this.clients.entries()) {
      if (clientData.imeiCodes?.includes(imei)) {
        // Send updated status for all IMEIs the client has access to
        const trackerStatus = this.gpsHandler.getTrackerConnectionStatus(clientData.imeiCodes);
        this.send(ws, {
          type: 'tracker_status',
          data: trackerStatus
        });
      }
    }
  }

  private handleVoyageListRequest(ws: WebSocket): void {
    const clientData = this.clients.get(ws);
    if (!clientData?.imeiCodes) {
      this.sendError(ws, 'Not authenticated');
      return;
    }

    const voyages = this.gpsHandler.getVoyagesForImei(clientData.imeiCodes);
    this.send(ws, {
      type: 'voyage_list',
      data: voyages
    });
  }

  private handleMessageListRequest(ws: WebSocket, requestData: any): void {
    const clientData = this.clients.get(ws);
    if (!clientData?.imeiCodes) {
      this.sendError(ws, 'Not authenticated');
      return;
    }

    let messages;
    if (requestData?.voyageId) {
      messages = this.gpsHandler.getVoyageMessages(requestData.voyageId);
    } else {
      messages = this.gpsHandler.getMessagesForImei(clientData.imeiCodes);
    }

    this.send(ws, {
      type: 'message_list',
      data: messages
    });
  }

  private handleTrackerStatusRequest(ws: WebSocket): void {
    const clientData = this.clients.get(ws);
    if (!clientData?.imeiCodes) {
      this.sendError(ws, 'Not authenticated');
      return;
    }

    const trackerStatus = this.gpsHandler.getTrackerConnectionStatus(clientData.imeiCodes);
    this.send(ws, {
      type: 'tracker_status',
      data: trackerStatus
    });
  }

  private broadcastToAuthorizedClients(imei: string, message: WebSocketMessage): void {
    for (const [ws, clientData] of this.clients.entries()) {
      if (clientData.imeiCodes?.includes(imei)) {
        this.send(ws, message);
      }
    }
  }

  private send(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string): void {
    this.send(ws, { type: 'error', error });
  }
}

// Legacy WebSocketManager class for backward compatibility
// This will be replaced by the new server architecture
export class WebSocketManager extends WebClientWebSocketServer {
  constructor(server: any, authService: AuthService, gpsHandler: GPSHandler) {
    super(server, authService, gpsHandler);
  }
}
