import WebSocket from 'ws';

// Test per verificare il rilevamento in tempo reale delle disconnessioni dei tracker GPS
class RealtimeDisconnectionTest {
  constructor() {
    this.clientUrl = 'ws://localhost:3000/ws';
    this.trackerUrl = 'ws://localhost:8090';
    this.testImei = '123456789012345';
    this.trackerWs = null;
    this.clientWs = null;
    this.testResults = [];
  }

  async runTest() {
    console.log('🧪 Avvio test rilevamento disconnessioni in tempo reale...\n');

    try {
      // Step 1: Connetti client web per monitorare lo stato
      await this.connectWebClient();
      
      // Step 2: Connetti tracker GPS
      await this.connectGPSTracker();
      
      // Step 3: Invia dati GPS per stabilire la connessione
      await this.sendGPSData();
      
      // Step 4: Verifica stato connesso
      await this.checkTrackerStatus(true, 'Tracker dovrebbe essere connesso');
      
      // Step 5: Disconnetti il tracker improvvisamente
      await this.disconnectTracker();
      
      // Step 6: Verifica rilevamento immediato della disconnessione
      await this.checkTrackerStatus(false, 'Tracker dovrebbe essere disconnesso immediatamente');
      
      // Step 7: Riconnetti il tracker
      await this.reconnectTracker();
      
      // Step 8: Invia nuovi dati GPS
      await this.sendGPSData();
      
      // Step 9: Verifica riconnessione immediata
      await this.checkTrackerStatus(true, 'Tracker dovrebbe essere riconnesso immediatamente');
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Errore durante il test:', error);
    } finally {
      this.cleanup();
    }
  }

  async connectWebClient() {
    return new Promise((resolve, reject) => {
      console.log('📱 Connessione client web...');
      this.clientWs = new WebSocket(this.clientUrl);
      
      this.clientWs.on('open', () => {
        console.log('✅ Client web connesso');
        
        // Autentica il client
        this.clientWs.send(JSON.stringify({
          type: 'auth',
          data: {
            username: 'admin',
            password: 'password'
          }
        }));
      });
      
      this.clientWs.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          
          if (message.type === 'auth' && message.data.success) {
            console.log('✅ Client web autenticato');
            resolve();
          }
          
          if (message.type === 'tracker_status') {
            console.log('📡 Stato tracker ricevuto:', message.data);
            this.lastTrackerStatus = message.data;
          }
          
        } catch (error) {
          console.error('Errore parsing messaggio client:', error);
        }
      });
      
      this.clientWs.on('error', reject);
      
      setTimeout(() => reject(new Error('Timeout connessione client')), 5000);
    });
  }

  async connectGPSTracker() {
    return new Promise((resolve, reject) => {
      console.log('🛰️ Connessione tracker GPS...');
      this.trackerWs = new WebSocket(this.trackerUrl);
      
      this.trackerWs.on('open', () => {
        console.log('✅ Tracker GPS connesso');
        resolve();
      });
      
      this.trackerWs.on('error', reject);
      
      setTimeout(() => reject(new Error('Timeout connessione tracker')), 5000);
    });
  }

  async sendGPSData() {
    return new Promise((resolve) => {
      console.log('📍 Invio dati GPS...');
      
      const gpsData = {
        type: 'gps_data',
        data: {
          imei: this.testImei,
          timestamp: new Date().toISOString(),
          lat: 40.7128 + Math.random() * 0.01,
          lng: -74.0060 + Math.random() * 0.01,
          speed: Math.random() * 100,
          status: 'Fixed',
          battery: 85
        }
      };
      
      this.trackerWs.send(JSON.stringify(gpsData));
      console.log('✅ Dati GPS inviati');
      
      // Aspetta un momento per il processing
      setTimeout(resolve, 1000);
    });
  }

  async disconnectTracker() {
    return new Promise((resolve) => {
      console.log('🔌 Disconnessione tracker...');
      
      this.trackerWs.close();
      this.trackerWs = null;
      console.log('✅ Tracker disconnesso');
      
      // Aspetta un momento per il processing
      setTimeout(resolve, 1000);
    });
  }

  async reconnectTracker() {
    console.log('🔄 Riconnessione tracker...');
    await this.connectGPSTracker();
  }

  async checkTrackerStatus(expectedConnected, description) {
    return new Promise((resolve) => {
      console.log(`🔍 Verifica: ${description}`);
      
      // Richiedi stato tracker
      this.clientWs.send(JSON.stringify({
        type: 'tracker_status'
      }));
      
      // Aspetta la risposta
      setTimeout(() => {
        const actualConnected = this.lastTrackerStatus && this.lastTrackerStatus[this.testImei];
        const success = actualConnected === expectedConnected;
        
        const result = {
          description,
          expected: expectedConnected,
          actual: actualConnected,
          success
        };
        
        this.testResults.push(result);
        
        if (success) {
          console.log(`✅ ${description} - SUCCESSO`);
        } else {
          console.log(`❌ ${description} - FALLITO (atteso: ${expectedConnected}, ricevuto: ${actualConnected})`);
        }
        
        resolve();
      }, 2000);
    });
  }

  printResults() {
    console.log('\n📊 RISULTATI TEST:');
    console.log('==================');
    
    let passed = 0;
    let total = this.testResults.length;
    
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${result.description}: ${status}`);
      if (result.success) passed++;
    });
    
    console.log(`\n📈 Risultato finale: ${passed}/${total} test passati`);
    
    if (passed === total) {
      console.log('🎉 TUTTI I TEST SONO PASSATI! Il rilevamento in tempo reale funziona correttamente.');
    } else {
      console.log('⚠️ Alcuni test sono falliti. Verificare l\'implementazione.');
    }
  }

  cleanup() {
    console.log('\n🧹 Pulizia...');
    
    if (this.trackerWs) {
      this.trackerWs.close();
    }
    
    if (this.clientWs) {
      this.clientWs.close();
    }
    
    console.log('✅ Pulizia completata');
  }
}

// Esegui il test
const test = new RealtimeDisconnectionTest();
test.runTest().then(() => {
  console.log('\n🏁 Test completato');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Test fallito:', error);
  process.exit(1);
});
