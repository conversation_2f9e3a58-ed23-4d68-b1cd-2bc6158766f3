import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebClientWebSocketServer } from './websocket.js';
import { AuthService } from './auth.js';
import { GPSHandler } from './gps-handler.js';
import path from 'path';

const app = express();
const server = createServer(app);
const WEB_CLIENT_PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize services
const authService = new AuthService();

// Web client server will receive GPS handler from main server
let gpsHandler: GPSHandler;
let webClientWsServer: WebClientWebSocketServer | null = null;

// Initialize web client server with shared GPS handler
const initializeWebClientServer = (sharedGpsHandler: GPSHandler) => {
  gpsHandler = sharedGpsHandler;
  webClientWsServer = new WebClientWebSocketServer(server, authService, gpsHandler);
  return webClientWsServer;
};

// Authentication endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    const token = await authService.login(username, password);
    
    if (token) {
      res.json({ token, success: true });
    } else {
      res.status(401).json({ error: 'Invalid credentials' });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, password, imeiCodes } = req.body;
    
    if (!username || !password || !imeiCodes || !Array.isArray(imeiCodes)) {
      return res.status(400).json({ 
        error: 'Username, password, and IMEI codes array are required' 
      });
    }

    const success = await authService.register(username, password, imeiCodes);
    
    if (success) {
      // Auto-login after registration
      const token = await authService.login(username, password);
      res.json({ token, success: true });
    } else {
      res.status(400).json({ error: 'Registration failed - user may already exist' });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/auth/verify', (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({ error: 'Token is required' });
    }

    const user = authService.verifyToken(token);
    
    if (user) {
      res.json({ valid: true, user });
    } else {
      res.status(401).json({ valid: false, error: 'Invalid token' });
    }
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check endpoint for web client server
app.get('/api/health', (_, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    server: 'Web Client Server',
    port: WEB_CLIENT_PORT
  });
});

// Debug endpoint for web client server
app.get('/api/debug', (_, res) => {
  if (!gpsHandler) {
    return res.status(503).json({ error: 'GPS Handler not initialized' });
  }
  
  const debugInfo = gpsHandler.getDebugInfo();
  res.json({
    ...debugInfo,
    server: 'Web Client Server',
    port: WEB_CLIENT_PORT
  });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static('dist'));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../dist/index.html'));
  });
}

// Start web client server
const startWebClientServer = () => {
  return new Promise<void>((resolve) => {
    server.listen(WEB_CLIENT_PORT, () => {
      console.log(`🌐 Web Client Server running on port ${WEB_CLIENT_PORT}`);
      console.log(`📱 WebSocket server ready for web client connections at ws://localhost:${WEB_CLIENT_PORT}/ws`);
      console.log(`🌍 Web interface available at http://localhost:${WEB_CLIENT_PORT}`);
      resolve();
    });
  });
};

// Graceful shutdown
const shutdownWebClientServer = () => {
  return new Promise<void>((resolve) => {
    console.log('🌐 Shutting down Web Client Server...');
    server.close(() => {
      console.log('🌐 Web Client Server closed');
      resolve();
    });
  });
};

// Process handlers are managed by the main server coordinator

// Getter function to access the web client server
const getWebClientServer = () => webClientWsServer;

export {
  webClientWsServer,
  authService,
  initializeWebClientServer,
  getWebClientServer,
  startWebClientServer,
  shutdownWebClientServer
};
