import WebSocket from 'ws';

console.log('🔍 Debug Frontend Data Reception');

const ws = new WebSocket('ws://localhost:3000/ws');

ws.on('open', () => {
  console.log('✅ WebSocket connected');
  
  // Authenticate
  console.log('🔐 Authenticating...');
  ws.send(JSON.stringify({
    type: 'auth',
    data: { username: 'testuser', password: 'password123' }
  }));
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log(`📥 Received message type: ${message.type}`);
    
    if (message.type === 'auth' && message.data?.success) {
      console.log('✅ Authentication successful');
      console.log('📋 IMEI codes:', message.data.imeiCodes);
      
      // Request voyage list
      console.log('🚢 Requesting voyage list...');
      ws.send(JSON.stringify({ type: 'voyage_list' }));
      
      // Request message list
      console.log('📨 Requesting message list...');
      ws.send(JSON.stringify({ type: 'message_list' }));
      
    } else if (message.type === 'voyage_list') {
      console.log('\n🚢 === VOYAGE LIST ===');
      const voyages = message.data;
      console.log(`Total voyages: ${voyages.length}`);
      
      voyages.forEach((voyage, index) => {
        console.log(`\nVoyage ${index + 1}:`);
        console.log(`  ID: ${voyage.id}`);
        console.log(`  IMEI: ${voyage.imei}`);
        console.log(`  Start: ${voyage.startTime}`);
        console.log(`  End: ${voyage.endTime || 'In progress'}`);
        console.log(`  Messages: ${voyage.messages.length}`);
        
        if (voyage.messages.length > 0) {
          console.log(`  First message: ${voyage.messages[0].status} at ${voyage.messages[0].latitude}, ${voyage.messages[0].longitude}`);
          console.log(`  Last message: ${voyage.messages[voyage.messages.length - 1].status} at ${voyage.messages[voyage.messages.length - 1].latitude}, ${voyage.messages[voyage.messages.length - 1].longitude}`);
        }
      });
      
    } else if (message.type === 'message_list') {
      console.log('\n📨 === MESSAGE LIST ===');
      const messages = message.data;
      console.log(`Total messages: ${messages.length}`);
      
      // Group messages by IMEI
      const messagesByImei = {};
      messages.forEach(msg => {
        if (!messagesByImei[msg.imei]) {
          messagesByImei[msg.imei] = [];
        }
        messagesByImei[msg.imei].push(msg);
      });
      
      Object.keys(messagesByImei).forEach(imei => {
        console.log(`\nIMEI ${imei}: ${messagesByImei[imei].length} messages`);
        const imeiMessages = messagesByImei[imei];
        
        // Show first and last few messages
        if (imeiMessages.length > 0) {
          console.log(`  First: ${imeiMessages[0].status} at ${imeiMessages[0].latitude}, ${imeiMessages[0].longitude}`);
          if (imeiMessages.length > 1) {
            console.log(`  Last: ${imeiMessages[imeiMessages.length - 1].status} at ${imeiMessages[imeiMessages.length - 1].latitude}, ${imeiMessages[imeiMessages.length - 1].longitude}`);
          }
        }
      });
      
      // Close connection after getting data
      setTimeout(() => {
        console.log('\n🔚 Closing connection');
        ws.close();
        process.exit(0);
      }, 1000);
      
    } else if (message.type === 'gps_data') {
      console.log(`📍 New GPS data: IMEI ${message.data.imei}, Status: ${message.data.status}, Coords: ${message.data.latitude}, ${message.data.longitude}`);
    }
    
  } catch (error) {
    console.log('📥 Raw message:', data.toString());
  }
});

ws.on('close', () => {
  console.log('❌ WebSocket connection closed');
});

ws.on('error', (error) => {
  console.error('🚨 WebSocket error:', error.message);
  process.exit(1);
});

// Handle interruption
process.on('SIGINT', () => {
  console.log('\n🛑 Manual interruption');
  ws.close();
  process.exit(0);
});
