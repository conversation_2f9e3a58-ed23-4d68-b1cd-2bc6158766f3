import express from 'express';
import { createServer } from 'http';
import { TrackerWebSocketServer } from './websocket.js';
import { GPSHandler } from './gps-handler.js';

const app = express();
const server = createServer(app);
const TRACKER_PORT = 8090;

// Middleware
app.use(express.json());

// Initialize GPS handler
const gpsHandler = new GPSHandler();

// Initialize tracker WebSocket server
const trackerWsServer = new TrackerWebSocketServer(server, gpsHandler);

// Health check endpoint for tracker server
app.get('/api/tracker/health', (_, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    server: 'GPS Tracker Server',
    port: TRACKER_PORT
  });
});

// Debug endpoint for tracker connections
app.get('/api/tracker/debug', (_, res) => {
  const debugInfo = gpsHandler.getDebugInfo();
  res.json({
    ...debugInfo,
    server: 'GPS Tracker Server',
    port: TRACKER_PORT
  });
});

// Start tracker server
const startTrackerServer = () => {
  return new Promise<void>((resolve) => {
    server.listen(TRACKER_PORT, () => {
      console.log(`📡 GPS Tracker Server running on port ${TRACKER_PORT}`);
      console.log(`🛰️ WebSocket server ready for GPS tracker connections at ws://localhost:${TRACKER_PORT}`);
      resolve();
    });
  });
};

// Graceful shutdown
const shutdownTrackerServer = () => {
  return new Promise<void>((resolve) => {
    console.log('📡 Shutting down GPS Tracker Server...');
    server.close(() => {
      console.log('📡 GPS Tracker Server closed');
      resolve();
    });
  });
};

// Process handlers are managed by the main server coordinator

export { trackerWsServer, gpsHandler, startTrackerServer, shutdownTrackerServer };
