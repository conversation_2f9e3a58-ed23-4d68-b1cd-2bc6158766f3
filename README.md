# GPS Tracker Management Application

A comprehensive TypeScript application for managing messages from various GPS trackers with real-time tracking, voyage management, and interactive mapping capabilities.

## Features

### Frontend
- **Responsive UI** with interactive map and message management
- **Real-time GPS tracking** with live position updates
- **Interactive map** displaying tracker locations and voyage tracks
- **Voyage management** with trip start/end tracking
- **Sortable and filterable message list** with comprehensive data display
- **Authentication system** with secure login/registration
- **IMEI code management** for tracker authorization
- **Light green color scheme** with user-friendly interface

### Backend
- **WebSocket server** on port 8090 for real-time GPS tracker connections
- **RESTful API** for authentication and configuration
- **In-memory data storage** with efficient message handling
- **JWT-based authentication** with secure token management
- **CSV-based user storage** for credentials and IMEI codes
- **Satellite message handling** with extensible protocol support

## Technology Stack

- **Frontend**: React 18, TypeScript, Vite, Leaflet Maps
- **Backend**: Node.js, Express, WebSocket, TypeScript
- **Authentication**: JWT, bcrypt
- **Data Storage**: In-memory (messages), CSV (users)
- **Real-time Communication**: WebSocket

## Installation

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Setup Instructions

1. **Clone or create the project directory**
   ```bash
   mkdir gps-tracker-management
   cd gps-tracker-management
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Development mode (runs both frontend and backend)**
   ```bash
   npm run dev
   ```

4. **Production build**
   ```bash
   npm run build
   npm run server:start
   ```

## Usage

### Getting Started

1. **Access the application**
   - Open your browser to `http://localhost:3000`
   - The backend WebSocket server runs on port 8090

2. **Create an account**
   - Click "Register" on the login screen
   - Enter username, password, and IMEI codes of your GPS trackers
   - IMEI codes should be comma-separated (e.g., "***************, ***************")

3. **Login and manage trackers**
   - Use your credentials to log in
   - The dashboard will display your authorized GPS trackers
   - Update IMEI codes anytime using the "Manage IMEI Codes" button

### Dashboard Features

#### Map Panel
- **Interactive map** showing all GPS tracker positions
- **Voyage tracks** displayed as connected lines when a voyage is selected
- **Color-coded markers** based on position status:
  - Green: Fixed position
  - Orange: No GPS fix
  - Light Green: Normal tracking
  - Red: Trip end
- **Click markers** to view detailed position information

#### Voyage Selector
- **List all voyages** for your authorized trackers
- **Select individual voyages** to view specific trip tracks
- **Show all messages** option to display all tracker data
- **Voyage details** including start/end times and duration

#### Message List
- **Sortable columns** (click headers to sort)
- **Filter by status** and IMEI code
- **Detailed information** for each GPS message:
  - Tracker timestamp (GPS device time)
  - Server timestamp (received time)
  - GPS coordinates (latitude/longitude)
  - Speed in km/h
  - Position status
  - Battery level percentage
- **Click rows** to highlight positions on the map

## GPS Tracker Integration

### WebSocket Connection
GPS trackers should connect to: `ws://your-server:8090` (no /ws path)
Web clients connect to: `ws://your-server:3000/ws`

### Message Format
Send GPS data in JSON format:
```json
{
  "type": "gps_data",
  "data": {
    "imei": "***************",
    "timestamp": "2024-01-15T10:30:00Z",
    "lat": 40.7128,
    "lng": -74.0060,
    "speed": 65.5,
    "status": "Fixed",
    "battery": 85
  }
}
```

### Status Values
- `"Fixed"` - GPS has a good fix
- `"No Fixed"` - GPS cannot get a fix
- `"Trip Start"` - Beginning of a new voyage
- `"Trip End"` - End of current voyage

## Satellite Message Handling

The application includes a separate satellite message handler (`server/satellite-handler.js`) that can be extended to support various satellite communication protocols:

### Supported Protocols (Extensible)
- Iridium Short Burst Data (SBD)
- Globalstar
- Custom satellite protocols

### Usage Example
```javascript
const SatelliteMessageHandler = require('./server/satellite-handler.js');
const satelliteHandler = new SatelliteMessageHandler();
satelliteHandler.initialize();
satelliteHandler.setGPSHandler(gpsHandlerInstance);
satelliteHandler.queueMessage(rawSatelliteMessage);
```

## API Endpoints

### Authentication
- `POST /api/register` - Create new user account
- `POST /api/login` - User authentication
- `POST /api/update-imei` - Update IMEI codes for user

### Health Check
- `GET /api/health` - Server status check

## Configuration

### Environment Variables
- `PORT` - Server port (default: 8090)
- `JWT_SECRET` - JWT signing secret (change in production)
- `NODE_ENV` - Environment mode

### File Storage
- `users.csv` - User credentials and IMEI codes (auto-created)

## Security Features

- **JWT token authentication** with expiration
- **Password hashing** using bcrypt
- **IMEI-based authorization** - users only see their authorized trackers
- **Secure WebSocket connections** with authentication
- **Input validation** and error handling

## Development

### Project Structure
```
├── server/                 # Backend code
│   ├── server.ts          # Main server file
│   ├── auth.ts            # Authentication logic
│   ├── gps-handler.ts     # GPS message processing
│   ├── websocket.ts       # WebSocket server
│   ├── types.ts           # TypeScript types
│   └── satellite-handler.js # Satellite message handling
├── src/                   # Frontend code
│   ├── components/        # React components
│   ├── types/            # TypeScript types
│   ├── utils/            # Utility functions
│   └── styles/           # CSS styles
├── package.json          # Dependencies and scripts
└── README.md            # This file
```

### Available Scripts
- `npm run dev` - Start development servers (frontend + backend)
- `npm run client:dev` - Start frontend development server only
- `npm run server:dev` - Start backend development server only
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## Troubleshooting

### Common Issues

1. **WebSocket connection failed**
   - Check if port 8090 is available
   - Verify firewall settings
   - Ensure backend server is running

2. **Map not loading**
   - Check internet connection (requires OpenStreetMap tiles)
   - Verify Leaflet CSS is loaded correctly

3. **GPS data not appearing**
   - Verify IMEI codes are correctly configured
   - Check WebSocket message format
   - Ensure user is authenticated

### Logs
- Server logs appear in the console when running `npm run dev`
- Browser console shows frontend errors and WebSocket connection status

## License

This project is provided as-is for GPS tracker management purposes. Customize and extend as needed for your specific requirements.

## Support

For technical support or customization requests, refer to the code comments and TypeScript type definitions for detailed implementation guidance.
