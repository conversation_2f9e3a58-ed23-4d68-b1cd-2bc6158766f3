import WebSocket from 'ws';

console.log('🔍 Debug Voyages - Verifica creazione viaggi');

const ws = new WebSocket('ws://localhost:3000/ws');

ws.on('open', () => {
  console.log('✅ Connesso al server GPS');
  
  // Prima fai login per ottenere un token
  const loginMessage = {
    type: 'auth',
    data: {
      username: '<PERSON>',
      password: 'password123'
    }
  };
  
  console.log('🔐 Invio login...');
  ws.send(JSON.stringify(loginMessage));
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log(`📥 Ricevuto messaggio tipo: ${message.type}`);
    
    if (message.type === 'auth' && message.data?.success) {
      console.log('✅ Autenticazione riuscita');
      console.log('📋 IMEI autorizzati:', message.data.imeiCodes);
      
      // Richiedi la lista dei viaggi
      console.log('🚢 Richiedo lista viaggi...');
      ws.send(JSON.stringify({ type: 'voyage_list' }));
      
      // Richiedi la lista dei messaggi
      console.log('📨 Richiedo lista messaggi...');
      ws.send(JSON.stringify({ type: 'message_list' }));
      
    } else if (message.type === 'voyage_list') {
      console.log('\n🚢 === LISTA VIAGGI ===');
      const voyages = message.data;
      
      if (voyages.length === 0) {
        console.log('❌ Nessun viaggio trovato!');
      } else {
        console.log(`✅ Trovati ${voyages.length} viaggi:`);
        
        voyages.forEach((voyage, index) => {
          console.log(`\n📍 Viaggio ${index + 1}:`);
          console.log(`   ID: ${voyage.id}`);
          console.log(`   IMEI: ${voyage.imei}`);
          console.log(`   Inizio: ${new Date(voyage.startTime).toLocaleString()}`);
          console.log(`   Fine: ${voyage.endTime ? new Date(voyage.endTime).toLocaleString() : 'In corso'}`);
          console.log(`   Messaggi: ${voyage.messages.length}`);
          
          // Mostra i primi e ultimi messaggi del viaggio
          if (voyage.messages.length > 0) {
            const firstMsg = voyage.messages[0];
            const lastMsg = voyage.messages[voyage.messages.length - 1];
            console.log(`   Primo messaggio: ${firstMsg.status} (${firstMsg.latitude}, ${firstMsg.longitude})`);
            console.log(`   Ultimo messaggio: ${lastMsg.status} (${lastMsg.latitude}, ${lastMsg.longitude})`);
          }
        });
      }
      
    } else if (message.type === 'message_list') {
      console.log('\n📨 === LISTA MESSAGGI ===');
      const messages = message.data;
      
      console.log(`📊 Totale messaggi: ${messages.length}`);
      
      // Raggruppa per IMEI
      const messagesByImei = {};
      messages.forEach(msg => {
        if (!messagesByImei[msg.imei]) {
          messagesByImei[msg.imei] = [];
        }
        messagesByImei[msg.imei].push(msg);
      });
      
      Object.keys(messagesByImei).forEach(imei => {
        const imeiMessages = messagesByImei[imei];
        console.log(`\n📱 IMEI ${imei}: ${imeiMessages.length} messaggi`);
        
        // Conta per status
        const statusCount = {};
        imeiMessages.forEach(msg => {
          statusCount[msg.status] = (statusCount[msg.status] || 0) + 1;
        });
        
        console.log('   Status breakdown:');
        Object.keys(statusCount).forEach(status => {
          console.log(`     ${status}: ${statusCount[status]}`);
        });
        
        // Mostra messaggi con voyageId
        const withVoyageId = imeiMessages.filter(msg => msg.voyageId);
        console.log(`   Messaggi con voyageId: ${withVoyageId.length}`);
      });
      
      // Chiudi connessione dopo aver ricevuto tutti i dati
      setTimeout(() => {
        console.log('\n🔚 Debug completato, chiudo connessione');
        ws.close();
        process.exit(0);
      }, 1000);
    }
    
  } catch (error) {
    console.log('📥 Messaggio non JSON:', data.toString());
  }
});

ws.on('close', () => {
  console.log('❌ Disconnesso dal server');
});

ws.on('error', (error) => {
  console.error('🚨 Errore WebSocket:', error.message);
  process.exit(1);
});

// Gestione interruzione
process.on('SIGINT', () => {
  console.log('\n🛑 Interruzione manuale');
  ws.close();
  process.exit(0);
});
