import WebSocket from 'ws';

console.log('🧪 Test WebSocket Client Connection');

const ws = new WebSocket('ws://localhost:3000/ws');

ws.on('open', () => {
  console.log('✅ WebSocket connected to server');
  
  // Test authentication
  console.log('🔐 Attempting authentication...');
  ws.send(JSON.stringify({
    type: 'auth',
    data: { username: 'testuser', password: 'password123' }
  }));
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📨 Received message:', message);
    
    if (message.type === 'auth' && message.data?.success) {
      console.log('✅ Authentication successful!');
      console.log('📋 Requesting voyage list...');
      
      // Request voyage list
      ws.send(JSON.stringify({
        type: 'voyage_list'
      }));
      
      // Request message list
      ws.send(JSON.stringify({
        type: 'message_list'
      }));
    }
    
    if (message.type === 'voyage_list') {
      console.log('🚢 Received voyage list:', message.data);
      console.log(`📊 Total voyages: ${message.data?.length || 0}`);
      
      if (message.data && message.data.length > 0) {
        message.data.forEach((voyage, index) => {
          console.log(`   Voyage ${index + 1}: ${voyage.id} (IMEI: ${voyage.imei}, Messages: ${voyage.messages?.length || 0})`);
        });
      }
    }
    
    if (message.type === 'message_list') {
      console.log('📝 Received message list:', message.data?.length || 0, 'messages');
    }
    
  } catch (error) {
    console.log('📥 Raw message:', data.toString());
  }
});

ws.on('close', () => {
  console.log('❌ WebSocket connection closed');
  process.exit(0);
});

ws.on('error', (error) => {
  console.error('🚨 WebSocket error:', error.message);
  process.exit(1);
});

// Close connection after 10 seconds
setTimeout(() => {
  console.log('⏰ Test timeout, closing connection');
  ws.close();
}, 10000);
