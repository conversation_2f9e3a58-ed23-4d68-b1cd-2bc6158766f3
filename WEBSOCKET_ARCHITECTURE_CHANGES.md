# WebSocket Server Architecture Separation

## Overview
Successfully separated the WebSocket server architecture into two distinct servers with different ports and purposes, as requested.

## Architecture Changes

### Before (Single Server)
- **Port 8090**: Single WebSocket server handling both GPS trackers and web clients
- **Path**: `/ws` for all connections
- **Issues**: Mixed connection types, potential conflicts, single point of failure

### After (Dual Server Architecture)

#### 1. GPS Tracker Server (Port 8090)
- **Purpose**: Dedicated WebSocket server exclusively for GPS tracker connections
- **Connection**: `ws://localhost:8090` (no `/ws` path)
- **Responsibilities**:
  - Receive GPS data from IoT devices
  - Process IMEI-based device identification
  - Handle GPS coordinate data processing
  - Manage tracker connection status
  - Immediate disconnection detection when trackers disconnect

#### 2. Web Client Server (Port 3000)
- **Purpose**: Separate WebSocket server exclusively for web frontend connections
- **Connection**: `ws://localhost:3000/ws`
- **Responsibilities**:
  - Serve web application and handle client-side WebSocket connections
  - Handle user authentication (token and username/password)
  - Process data requests (voyages, messages, tracker status)
  - Broadcast real-time updates to connected web clients
  - Maintain IMEI filtering and authorization

## Implementation Details

### New Files Created
1. **`server/tracker-server.ts`**: GPS tracker server implementation
2. **`server/web-client-server.ts`**: Web client server implementation
3. **`server/websocket.ts`**: Updated with separate classes:
   - `TrackerWebSocketServer`: Handles GPS tracker connections
   - `WebClientWebSocketServer`: Handles web client connections

### Modified Files
1. **`server/server.ts`**: Main coordinator that starts both servers
2. **`src/components/Dashboard.tsx`**: Updated to connect to port 3000
3. **Test files**: Updated to use correct ports
4. **Documentation**: Updated README and usage examples

### Inter-Server Communication
- **Data Flow**: GPS Tracker Server → Web Client Server → Web Clients
- **Method**: Direct method calls between server instances
- **Real-time**: GPS data is immediately broadcast to authorized web clients

## Connection Details

### GPS Trackers
```javascript
// GPS trackers connect to port 8090 without /ws path
const ws = new WebSocket('ws://localhost:8090');
```

### Web Clients
```javascript
// Web clients connect to port 3000 with /ws path
const ws = new WebSocket('ws://localhost:3000/ws');
```

## Features Preserved
- ✅ Real-time GPS tracking and trip visualization
- ✅ IMEI-based filtering and authorization
- ✅ Voyage creation and management
- ✅ Message table with date-time filtering
- ✅ Tracker connection status indicators
- ✅ CSV-based authentication
- ✅ Three-panel layout with map and message table
- ✅ Automatic disconnection detection
- ✅ Color-coded trip visualization

## Testing Results

### GPS Tracker Connection (Port 8090)
```
✅ GPS trackers successfully connect to ws://localhost:8090
✅ GPS data processing and voyage creation working
✅ Immediate disconnection detection functional
✅ Real-time data broadcasting to web clients
```

### Web Client Connection (Port 3000)
```
✅ Web clients successfully connect to ws://localhost:3000/ws
✅ Authentication working (both token and username/password)
✅ Voyage and message data retrieval functional
✅ Real-time GPS updates received from tracker server
✅ IMEI filtering and authorization working
```

### Inter-Server Communication
```
✅ GPS data flows from tracker server to web client server
✅ Real-time broadcasting to authorized web clients
✅ Tracker status changes propagated correctly
```

## Benefits Achieved

1. **Separation of Concerns**: Clear distinction between tracker and client handling
2. **Scalability**: Each server can be scaled independently
3. **Security**: Different authentication mechanisms for different connection types
4. **Reliability**: Failure in one server doesn't affect the other
5. **Maintainability**: Cleaner code organization and easier debugging
6. **Protocol Flexibility**: Different protocols can be implemented for each server type

## Startup Instructions

### Development
```bash
npm run dev  # Starts both servers and frontend
```

### Production
```bash
npm run build
npm start
```

### Manual Server Testing
```bash
# Test GPS tracker connection
node test-stefano.js

# Test web client connection
node debug-frontend.js
```

## Port Summary
- **8090**: GPS Tracker WebSocket Server (no /ws path)
- **3000**: Web Client WebSocket Server (/ws path) + Web Interface
- **Frontend**: Served from port 3000 in development and production

The architecture successfully maintains all existing functionality while providing the requested separation of concerns and improved scalability.
