import WebSocket from 'ws';

// Test per verificare il rilevamento immediato delle disconnessioni
console.log('🧪 Test rilevamento disconnessioni in tempo reale');

let trackerWs = null;
let monitorWs = null;
let trackerStatus = {};
let testStartTime = null;

// Connessione per monitorare lo stato dei tracker
function connectMonitor() {
  return new Promise((resolve, reject) => {
    console.log('📱 Connessione monitor client...');
    monitorWs = new WebSocket('ws://localhost:3000/ws');
    
    monitorWs.on('open', () => {
      console.log('✅ Monitor connesso');
      
      // Autentica come testuser
      monitorWs.send(JSON.stringify({
        type: 'auth',
        data: {
          username: 'testuser',
          password: 'password'
        }
      }));
    });
    
    monitorWs.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'auth' && message.data.success) {
          console.log('✅ Monitor autenticato');
          resolve();
        }
        
        if (message.type === 'tracker_status') {
          const oldStatus = JSON.stringify(trackerStatus);
          trackerStatus = message.data;
          const newStatus = JSON.stringify(trackerStatus);
          
          if (oldStatus !== newStatus) {
            const timestamp = new Date().toISOString();
            console.log(`📡 [${timestamp}] Stato tracker aggiornato:`, trackerStatus);
            
            // Se abbiamo un tempo di inizio test, calcola il tempo di rilevamento
            if (testStartTime) {
              const detectionTime = Date.now() - testStartTime;
              console.log(`⏱️ Tempo di rilevamento: ${detectionTime}ms`);
            }
          }
        }
        
      } catch (error) {
        console.error('Errore parsing messaggio monitor:', error);
      }
    });
    
    monitorWs.on('error', reject);
    
    setTimeout(() => reject(new Error('Timeout connessione monitor')), 5000);
  });
}

// Connessione simulatore GPS tracker
function connectTracker() {
  return new Promise((resolve, reject) => {
    console.log('🛰️ Connessione tracker GPS...');
    trackerWs = new WebSocket('ws://localhost:8090');
    
    trackerWs.on('open', () => {
      console.log('✅ Tracker GPS connesso');
      resolve();
    });
    
    trackerWs.on('error', reject);
    
    setTimeout(() => reject(new Error('Timeout connessione tracker')), 5000);
  });
}

// Invia dati GPS per stabilire la connessione
function sendGPSData() {
  const gpsData = {
    type: 'gps_data',
    data: {
      imei: '1234567890',
      timestamp: new Date().toISOString(),
      lat: 45.4642,
      lng: 9.1900,
      speed: 0,
      status: 'Inizio',
      battery: 95
    }
  };
  
  console.log('📍 Invio dati GPS...');
  trackerWs.send(JSON.stringify(gpsData));
}

// Richiedi stato tracker
function requestTrackerStatus() {
  console.log('🔍 Richiesta stato tracker...');
  monitorWs.send(JSON.stringify({
    type: 'tracker_status'
  }));
}

// Test principale
async function runTest() {
  try {
    console.log('\n=== FASE 1: Connessione e setup ===');
    
    // Connetti monitor
    await connectMonitor();
    
    // Connetti tracker
    await connectTracker();
    
    console.log('\n=== FASE 2: Stabilisci connessione tracker ===');
    
    // Invia dati GPS per stabilire la connessione
    sendGPSData();
    
    // Aspetta un momento per il processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Verifica stato connesso
    requestTrackerStatus();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (trackerStatus['1234567890'] === true) {
      console.log('✅ Tracker risulta connesso');
    } else {
      console.log('❌ Tracker non risulta connesso:', trackerStatus);
    }
    
    console.log('\n=== FASE 3: Test disconnessione improvvisa ===');
    
    // Registra il tempo di inizio del test
    testStartTime = Date.now();
    console.log('🔌 Disconnessione improvvisa del tracker...');
    
    // Chiudi la connessione del tracker improvvisamente
    trackerWs.close();
    trackerWs = null;
    
    // Monitora per 10 secondi per vedere quando viene rilevata la disconnessione
    console.log('⏳ Monitoraggio per 10 secondi...');
    
    let detectionTime = null;
    const startTime = Date.now();
    
    while (Date.now() - startTime < 10000) {
      requestTrackerStatus();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (trackerStatus['1234567890'] === false && !detectionTime) {
        detectionTime = Date.now() - testStartTime;
        console.log(`🎯 DISCONNESSIONE RILEVATA! Tempo: ${detectionTime}ms`);
        break;
      }
    }
    
    console.log('\n=== RISULTATI TEST ===');
    
    if (detectionTime) {
      console.log(`✅ SUCCESSO: Disconnessione rilevata in ${detectionTime}ms`);
      
      if (detectionTime < 5000) {
        console.log('🚀 ECCELLENTE: Rilevamento in tempo reale (< 5 secondi)');
      } else {
        console.log('⚠️ LENTO: Rilevamento tramite timeout (≥ 5 secondi)');
      }
    } else {
      console.log('❌ FALLIMENTO: Disconnessione non rilevata entro 10 secondi');
    }
    
    console.log('\n=== FASE 4: Test riconnessione ===');
    
    // Test riconnessione
    await connectTracker();
    sendGPSData();
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    requestTrackerStatus();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (trackerStatus['1234567890'] === true) {
      console.log('✅ Riconnessione rilevata correttamente');
    } else {
      console.log('❌ Riconnessione non rilevata');
    }
    
  } catch (error) {
    console.error('❌ Errore durante il test:', error);
  } finally {
    // Cleanup
    console.log('\n🧹 Pulizia...');
    if (trackerWs) trackerWs.close();
    if (monitorWs) monitorWs.close();
    
    setTimeout(() => {
      console.log('🏁 Test completato');
      process.exit(0);
    }, 1000);
  }
}

// Avvia il test
runTest();
